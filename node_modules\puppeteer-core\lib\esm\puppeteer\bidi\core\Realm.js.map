{"version": 3, "file": "Realm.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Realm.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAC1E,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAuBxE;;GAEG;IACmB,KAAK;sBAAS,YAAY;;;;;;;iBAA1B,KAAM,SAAQ,WASlC;;;YAuBA,wKAAU,OAAO,6DAGhB;YAMD,qKAAM,MAAM,6DAKX;YAMD,uLAAM,YAAY,6DAYjB;YAMD,2KAAM,QAAQ,6DAYb;YAMD,8NAAM,yBAAyB,6DAU9B;;;QAxFD,OAAO,GAVa,mDAAK,CAUR;QACE,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,EAAE,CAAS;QACX,MAAM,CAAS;QACd,kBAAkB,CAAU;QAEtC,YAAsB,EAAU,EAAE,MAAc;YAC9C,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QAED,IAAI,MAAM;YACR,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAC,CAAC;QAC1B,CAAC;QAGS,OAAO,CAAC,MAAe;YAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,MAAM,CAAC,OAAiB;YAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,YAAY,CAChB,mBAA2B,EAC3B,YAAqB,EACrB,UAA+B,EAAE;YAEjC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC9D,mBAAmB;gBACnB,YAAY;gBACZ,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,QAAQ,CACZ,UAAkB,EAClB,YAAqB,EACrB,UAA2B,EAAE;YAE7B,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC1D,UAAU;gBACV,YAAY;gBACZ,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,yBAAyB;YAC7B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,MAAM,EAAC,MAAM,EAAC,GAAG,MAAO,IAAI,CAAC,OAAO,CAAC,UAA6B,CAAC,IAAI,CACrE,uBAAuB,EACvB,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAC,CACjB,CAAC;gBACF,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;YACtD,CAAC;YAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,CAAC;QAEQ,yBArER,eAAe,yBAMf,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,+BAQD,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,2BAeD,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,4CAeD,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,GAaQ,aAAa,EAAC;YACtB,IAAI,CAAC,OAAO;gBACV,oFAAoF,CAAC;YACvF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE/C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SA3GmB,KAAK;AA8G3B;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,KAAK;IACpC,MAAM,CAAC,IAAI,CAAC,OAAwB,EAAE,OAAgB;QACpD,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEQ,eAAe,CAAkB;IACjC,OAAO,CAAU;IAEjB,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;IAE5D,YAAoB,OAAwB,EAAE,OAAgB;QAC5D,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEd,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,WAAW;QACT,MAAM,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CACjD,IAAI,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CACvC,CAAC;QACF,sBAAsB,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IACE,IAAI,CAAC,IAAI,KAAK,QAAQ;gBACtB,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,eAAe,CAAC,EAAE;gBACxC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAC7B,CAAC;gBACD,OAAO;YACT,CAAC;YACA,IAAY,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;IAC1D,CAAC;IAED,IAAa,MAAM;QACjB,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;IACnE,CAAC;CACF;AAUD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,KAAK;IAC7C,MAAM,CAAC,IAAI,CACT,KAAgC,EAChC,EAAU,EACV,MAAc;QAEd,MAAM,KAAK,GAAG,IAAI,EAAoB,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAC1D,KAAK,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEQ,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;IACnD,MAAM,CAAiC;IAEhD,YACE,KAAgC,EAChC,EAAU,EACV,MAAc;QAEd,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,WAAW;QACT,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE;YAChD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,EAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,yCAAyC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,CAAC,OAAO,CAAC;IACpD,CAAC;CACF;;AAED;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,KAAK;IAC1C,MAAM,CAAC,IAAI,CAAC,OAAgB,EAAE,EAAU,EAAE,MAAc;QACtD,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACzD,KAAK,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEQ,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;IACnD,OAAO,CAAU;IAE1B,YAAoB,OAAgB,EAAE,EAAU,EAAE,MAAc;QAC9D,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,WAAW;QACT,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE;YAChD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,CAAC;CACF"}