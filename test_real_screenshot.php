<?php
/**
 * تست واقعی گرفتن اسکرین‌شات از تلگرام
 */

require_once 'telegram_screenshot.php';

echo "=== تست واقعی اسکرین‌شات تلگرام ===\n\n";

// ایجاد نمونه از کلاس
$screenshot = new TelegramScreenshot();

// URL تست
$testUrl = 'https://t.me/Cd_mium/19354';

echo "در حال گرفتن اسکرین‌شات از: $testUrl\n";
echo "لطفاً صبر کنید...\n\n";

// گرفتن اسکرین‌شات
$result = $screenshot->takeScreenshot($testUrl, 'test_screenshot');

// نمایش نتیجه
if ($result['success']) {
    echo "✅ موفقیت!\n";
    echo "پیام: " . $result['message'] . "\n";
    echo "نام فایل: " . $result['filename'] . "\n";
    echo "مسیر: " . $result['path'] . "\n";
    
    if (isset($result['size'])) {
        $sizeKB = round($result['size'] / 1024, 2);
        echo "حجم: " . $sizeKB . " KB\n";
    }
    
    if (isset($result['url'])) {
        echo "URL: " . $result['url'] . "\n";
    }
    
    // بررسی وجود فایل
    if (file_exists($result['path'])) {
        echo "✅ فایل در مسیر مشخص شده موجود است\n";
        
        // نمایش اطلاعات فایل
        $fileInfo = getimagesize($result['path']);
        if ($fileInfo) {
            echo "ابعاد تصویر: " . $fileInfo[0] . "x" . $fileInfo[1] . " پیکسل\n";
            echo "نوع فایل: " . $fileInfo['mime'] . "\n";
        }
    } else {
        echo "❌ فایل در مسیر مشخص شده موجود نیست\n";
    }
    
} else {
    echo "❌ خطا!\n";
    echo "پیام خطا: " . $result['error'] . "\n";
}

echo "\n=== لیست تمام اسکرین‌شات‌ها ===\n";
$screenshots = $screenshot->listScreenshots();

if (empty($screenshots)) {
    echo "هیچ اسکرین‌شاتی یافت نشد.\n";
} else {
    foreach ($screenshots as $shot) {
        echo "📸 " . $shot['filename'] . "\n";
        echo "   حجم: " . round($shot['size'] / 1024, 2) . " KB\n";
        echo "   تاریخ: " . $shot['created'] . "\n";
        echo "   مسیر: " . $shot['path'] . "\n\n";
    }
}

echo "تست تمام شد!\n";
?>
