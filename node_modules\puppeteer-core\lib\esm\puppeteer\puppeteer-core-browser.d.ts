/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
export * from './index-browser.js';
import { Puppeteer } from './common/Puppeteer.js';
/**
 * @public
 */
declare const puppeteer: Puppeteer;
export declare const 
/**
 * @public
 */
connect: (options: import("./index.js").ConnectOptions) => Promise<import("./index.js").Browser>;
export default puppeteer;
//# sourceMappingURL=puppeteer-core-browser.d.ts.map