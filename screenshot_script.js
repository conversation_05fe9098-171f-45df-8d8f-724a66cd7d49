const puppeteer = require('puppeteer');
const fs = require('fs');

async function takeScreenshot(url, outputPath) {
    let browser;
    try {
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        const page = await browser.newPage();

        // تنظیم User Agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        // تنظیم viewport
        await page.setViewport({ width: 1200, height: 800 });

        // رفتن به صفحه
        await page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // صبر برای لود کامل محتوا
        await new Promise(resolve => setTimeout(resolve, 3000));

        // گرفتن اسکرین‌شات
        await page.screenshot({
            path: outputPath,
            fullPage: true,
            type: 'png'
        });

        console.log('SUCCESS');

    } catch (error) {
        console.error('ERROR: ' + error.message);
        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// دریافت آرگومان‌ها
const url = process.argv[2];
const outputPath = process.argv[3];

if (!url || !outputPath) {
    console.error('ERROR: URL and output path are required');
    process.exit(1);
}

takeScreenshot(url, outputPath);