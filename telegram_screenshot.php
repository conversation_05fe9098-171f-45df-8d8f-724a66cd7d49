<?php
/**
 * Telegram Channel Screenshot System
 *
 * این سیستم برای گرفتن اسکرین‌شات از کانال‌های تلگرام طراحی شده است
 *
 * نیازمندی‌ها:
 * - Puppeteer یا Chrome/Chromium
 * - Node.js
 * - دسترسی به اینترنت
 */

class TelegramScreenshot {

    private $shotsDir;
    private $nodeScriptPath;
    private $maxRetries;
    private $timeout;

    public function __construct() {
        $this->shotsDir = __DIR__ . '/shots/';
        $this->nodeScriptPath = __DIR__ . '/screenshot_script.js';
        $this->maxRetries = 3;
        $this->timeout = 30; // seconds

        // اطمینان از وجود پوشه shots
        if (!is_dir($this->shotsDir)) {
            mkdir($this->shotsDir, 0755, true);
        }
    }

    /**
     * گرفتن اسکرین‌شات از لینک تلگرام
     *
     * @param string $telegramUrl لینک کانال تلگرام
     * @param string $customName نام دلخواه برای فایل (اختیاری)
     * @return array نتیجه عملیات
     */
    public function takeScreenshot($telegramUrl, $customName = null) {
        try {
            // اعتبارسنجی URL
            if (!$this->isValidTelegramUrl($telegramUrl)) {
                return [
                    'success' => false,
                    'error' => 'لینک تلگرام معتبر نیست'
                ];
            }

            // تولید نام فایل
            $filename = $this->generateFilename($telegramUrl, $customName);
            $fullPath = $this->shotsDir . $filename;

            // بررسی وجود فایل
            if (file_exists($fullPath)) {
                return [
                    'success' => true,
                    'message' => 'اسکرین‌شات از قبل موجود است',
                    'filename' => $filename,
                    'path' => $fullPath,
                    'url' => $this->getFileUrl($filename)
                ];
            }

            // ایجاد اسکریپت Node.js
            $this->createNodeScript();

            // اجرای اسکرین‌شات
            $result = $this->executeScreenshot($telegramUrl, $fullPath);

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'اسکرین‌شات با موفقیت گرفته شد',
                    'filename' => $filename,
                    'path' => $fullPath,
                    'url' => $this->getFileUrl($filename),
                    'size' => filesize($fullPath)
                ];
            } else {
                return $result;
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'خطا در گرفتن اسکرین‌شات: ' . $e->getMessage()
            ];
        }
    }

    /**
     * اعتبارسنجی URL تلگرام
     */
    private function isValidTelegramUrl($url) {
        // بررسی فرمت کلی URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // بررسی دامنه تلگرام
        $pattern = '/^https?:\/\/(t\.me|telegram\.me)\/[a-zA-Z0-9_]+\/?(\d+)?$/';
        return preg_match($pattern, $url) === 1;
    }

    /**
     * تولید نام فایل
     */
    private function generateFilename($url, $customName = null) {
        if ($customName) {
            $name = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $customName);
        } else {
            // استخراج نام کانال از URL
            preg_match('/\/([a-zA-Z0-9_]+)\/?\d*$/', $url, $matches);
            $channelName = isset($matches[1]) ? $matches[1] : 'telegram';
            $name = $channelName . '_' . date('Y-m-d_H-i-s');
        }

        return $name . '.png';
    }

    /**
     * ایجاد اسکریپت Node.js برای اسکرین‌شات
     */
    private function createNodeScript() {
        $script = <<<'JS'
const puppeteer = require('puppeteer');
const fs = require('fs');

async function takeScreenshot(url, outputPath) {
    let browser;
    try {
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        const page = await browser.newPage();

        // تنظیم User Agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        // تنظیم viewport
        await page.setViewport({ width: 1200, height: 800 });

        // رفتن به صفحه
        await page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // صبر برای لود کامل محتوا
        await new Promise(resolve => setTimeout(resolve, 3000));

        // گرفتن اسکرین‌شات
        await page.screenshot({
            path: outputPath,
            fullPage: true,
            type: 'png'
        });

        console.log('SUCCESS');

    } catch (error) {
        console.error('ERROR: ' + error.message);
        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// دریافت آرگومان‌ها
const url = process.argv[2];
const outputPath = process.argv[3];

if (!url || !outputPath) {
    console.error('ERROR: URL and output path are required');
    process.exit(1);
}

takeScreenshot(url, outputPath);
JS;

        file_put_contents($this->nodeScriptPath, $script);
    }

    /**
     * اجرای اسکرین‌شات
     */
    private function executeScreenshot($url, $outputPath) {
        $command = "node " . escapeshellarg($this->nodeScriptPath) . " " .
                   escapeshellarg($url) . " " . escapeshellarg($outputPath) . " 2>&1";

        $output = [];
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $outputString = implode("\n", $output);

        if ($returnCode === 0 && strpos($outputString, 'SUCCESS') !== false) {
            if (file_exists($outputPath) && filesize($outputPath) > 0) {
                return ['success' => true];
            } else {
                return [
                    'success' => false,
                    'error' => 'فایل اسکرین‌شات ایجاد نشد'
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => 'خطا در اجرای اسکرین‌شات: ' . $outputString
            ];
        }
    }

    /**
     * دریافت URL فایل
     */
    private function getFileUrl($filename) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $scriptDir = dirname($_SERVER['SCRIPT_NAME']);

        return $protocol . '://' . $host . $scriptDir . '/shots/' . $filename;
    }

    /**
     * لیست تمام اسکرین‌شات‌ها
     */
    public function listScreenshots() {
        $files = glob($this->shotsDir . '*.png');
        $screenshots = [];

        foreach ($files as $file) {
            $filename = basename($file);
            $screenshots[] = [
                'filename' => $filename,
                'path' => $file,
                'url' => $this->getFileUrl($filename),
                'size' => filesize($file),
                'created' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }

        return $screenshots;
    }

    /**
     * حذف اسکرین‌شات
     */
    public function deleteScreenshot($filename) {
        $filePath = $this->shotsDir . $filename;

        if (file_exists($filePath)) {
            if (unlink($filePath)) {
                return ['success' => true, 'message' => 'فایل حذف شد'];
            } else {
                return ['success' => false, 'error' => 'خطا در حذف فایل'];
            }
        } else {
            return ['success' => false, 'error' => 'فایل یافت نشد'];
        }
    }
}

// استفاده از کلاس در صورت فراخوانی مستقیم
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    header('Content-Type: application/json; charset=utf-8');

    $screenshot = new TelegramScreenshot();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $url = $input['url'] ?? $_POST['url'] ?? '';
        $customName = $input['name'] ?? $_POST['name'] ?? null;

        if (empty($url)) {
            echo json_encode([
                'success' => false,
                'error' => 'لینک تلگرام الزامی است'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }

        $result = $screenshot->takeScreenshot($url, $customName);
        echo json_encode($result, JSON_UNESCAPED_UNICODE);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        if (isset($_GET['list'])) {
            $screenshots = $screenshot->listScreenshots();
            echo json_encode([
                'success' => true,
                'screenshots' => $screenshots
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'متد درخواست پشتیبانی نمی‌شود'
            ], JSON_UNESCAPED_UNICODE);
        }

    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        $input = json_decode(file_get_contents('php://input'), true);
        $filename = $input['filename'] ?? '';

        if (empty($filename)) {
            echo json_encode([
                'success' => false,
                'error' => 'نام فایل الزامی است'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }

        $result = $screenshot->deleteScreenshot($filename);
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
    }
}
?>